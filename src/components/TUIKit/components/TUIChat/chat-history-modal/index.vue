<template>
  <!-- 使用 Transition 组件包装弹窗 -->
  <Transition name="modal" appear>
    <div v-if="visible" class="chat-history-modal" @click="handleMaskClick">
      <Transition name="slide-up" appear>
        <div v-if="visible" class="chat-history-modal__container" @click.stop>
          <!-- 头部 -->
          <div class="chat-history-modal__header">
            <div class="chat-history-modal__title">历史对话</div>
            <div class="chat-history-modal__close" @click="handleClose">
              <img :src="closeIcon" alt="" />
            </div>
          </div>

          <!-- 内容区域 -->
          <div class="chat-history-modal__content">
            <!-- 加载状态 -->
            <div v-if="loading" class="chat-history-modal__loading">
              <div class="loading-spinner"></div>
              <span>加载中...</span>
            </div>

            <!-- 空状态 -->
            <div v-else-if="!loading && historyList.length === 0" class="chat-history-modal__empty">
              <div class="empty-icon">💬</div>
              <p>暂无历史对话</p>
            </div>

            <!-- 历史对话列表 -->
            <div v-else class="chat-history-modal__list">
              <div class="scroller" @scroll="handleScroll">
                <div v-for="item in historyList" :key="item.sessionId" class="chat-history-item"
                  :class="{ 'chat-history-item--active': item.sessionId === chatStore.sessionId }"
                  @click="handleItemClick(item)">
                  <!-- 头像 -->
                  <div class="chat-history-item__avatar">
                    <img :src="item.sessionId === chatStore.sessionId ? messageActiveIcon : messageIcon"
                      :alt="item.sessionName" class="avatar-img" />
                  </div>

                  <!-- 对话信息 -->
                  <div class="chat-history-item__info">
                    <div class="chat-history-item__title">
                      {{ item.sessionName }}
                    </div>
                    <div class="chat-history-item__time">
                      {{ formatFullTime(item.lastMessageTime) }}
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="chat-history-item__actions">
                    <div class="action-btn edit-btn" @click.stop="handleEdit(item)">
                      <img :src="item.sessionId === chatStore.sessionId ? editActiveIcon : editIcon" alt="编辑" />
                    </div>
                    <div class="action-btn delete-btn" @click.stop="handleDelete(item)">
                      <img :src="item.sessionId === chatStore.sessionId ? deleteActiveIcon : deleteIcon" alt="删除" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 加载更多指示器 -->
            <div v-if="loadingMore" class="chat-history-modal__loading-more">
              <div class="loading-spinner"></div>
              <span>加载更多...</span>
            </div>

            <!-- 没有更多数据提示 -->
            <div v-else-if="!hasMore && historyList.length > 0" class="chat-history-modal__no-more">
              <span>没有更多数据了</span>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>

  <!-- 编辑对话名称弹窗 -->
  <Transition name="modal" appear>
    <div v-if="showEditModal" class="edit-modal" @click="handleEditMaskClick">
      <div class="edit-dialog" @click.stop>
        <!-- 头部 -->
        <div class="edit-header">
          <div class="edit-title">修改对话名称</div>
          <div class="edit-close" @click="cancelEdit">
            <img :src="closeDeleteIcon" alt="" />
          </div>
        </div>

        <!-- 输入框 -->
        <div class="edit-content">
          <input ref="editInput" v-model="editName" type="text" class="edit-input" placeholder="请输入对话名称" maxlength="50"
            @keyup.enter="confirmEdit" @keyup.esc="cancelEdit" />
        </div>

        <!-- 底部按钮 -->
        <div class="edit-footer">
          <button class="btn btn-cancel" @click="cancelEdit">取消</button>
          <button class="btn btn-confirm" @click="confirmEdit" :disabled="!editName.trim()">确定</button>
        </div>
      </div>
    </div>
  </Transition>

  <!-- 删除确认弹窗 -->
  <Transition name="modal" appear>
    <div v-if="showDeleteConfirm" class="delete-confirm-modal" @click="handleDeleteMaskClick">
      <div class="delete-confirm-dialog" @click.stop>
        <!--头部-->
        <div class="header">
          <div class="delete-confirm-title">确定删除对话？</div>
          <div class="close" @click.stop="cancelDelete">
            <img :src="closeDeleteIcon" alt="" />
          </div>
        </div>
        <!--内容-->
        <div class="delete-confirm-content">删除后，聊天记录将不可恢复</div>
        <!-- 底部按钮 -->
        <div class="delete-confirm-footer">
          <button class="btn btn-cancel" @click="cancelDelete">取消</button>
          <button class="btn btn-confirm" @click="confirmDelete">删除</button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script lang="ts" setup>
import useChatStore from '@/store/modules/chat'
import { ref, onMounted, onUnmounted, watch, nextTick, Transition } from '../../../adapter-vue'
import { type HistoryConversationItem, getIMSessionList, modifySessionName, deleteSession, createGroupSession } from '@/api/chatHistory'
import { ElMessage } from 'element-plus'

import closeIcon from '@/assets/images/history-chat/close.png'
import closeDeleteIcon from '@/assets/images/delete/close.png'
import messageIcon from '@/assets/images/history-chat/message.png'
import messageActiveIcon from '@/assets/images/history-chat/message-active.png'
import editIcon from '@/assets/images/history-chat/edit.png'
import editActiveIcon from '@/assets/images/history-chat/edit-active.png'
import deleteIcon from '@/assets/images/history-chat/delete.png'
import deleteActiveIcon from '@/assets/images/history-chat/delete-active.png'
// 使用API接口中定义的类型
type HistoryItem = HistoryConversationItem

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})
const chatStore = useChatStore()
// Emits定义
const emit = defineEmits(['close', 'select'])
// 响应式数据
const loading = ref(false)
const historyList = ref<HistoryItem[]>([])
const showDeleteConfirm = ref(false)
const currentDeleteItem = ref<HistoryItem | null>(null)

// 分页相关状态
const loadingMore = ref(false)
const hasMore = ref(true)
const currentMain = ref<string | number>(0)




// 编辑弹窗相关数据
const showEditModal = ref(false)
const editName = ref('')
const currentEditItem = ref<HistoryItem | null>(null)
const editInput = ref<HTMLInputElement | null>(null)

// 格式化完整时间（年-月-日 时:分:秒）
// 1753501475399 示例数据 转换方法
const formatFullTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
    .replace(/\//g, '-')
}

// 事件处理
const handleClose = () => {
  emit('close')
}

const handleMaskClick = () => {
  handleClose()
}

const handleItemClick = (item: HistoryItem) => {
  // 在 state 里记录下 sessionId
  chatStore.setSessionId(item.sessionId)
  emit('select', item)
  handleClose()
}

// 编辑按钮点击事件
const handleEdit = (item: HistoryItem) => {
  currentEditItem.value = item
  editName.value = item.sessionName
  showEditModal.value = true

  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    if (editInput.value) {
      editInput.value.focus()
      editInput.value.select() // 选中所有文本
    }
  })
}

// 编辑弹窗相关方法
const handleEditMaskClick = () => {
  cancelEdit()
}

const cancelEdit = () => {
  showEditModal.value = false
  editName.value = ''
  currentEditItem.value = null
}

const confirmEdit = async () => {
  if (!editName.value.trim() || !currentEditItem.value) {
    ElMessage.warning('请输入有效的会话名称')
    return
  }

  try {
    const trimmedName = editName.value.trim()
    const response: any = await modifySessionName({
      sessionId: currentEditItem.value.sessionId,
      sessionName: trimmedName,
    })

    if (response.code === 200) {
      // 更新本地数据
      const index = historyList.value.findIndex(item => item.sessionId === currentEditItem.value?.sessionId)
      if (index > -1) {
        historyList.value[index].sessionName = trimmedName
      }

      ElMessage.success('会话名称修改成功')
      console.log('会话名称修改成功:', {
        sessionId: currentEditItem.value.sessionId,
        oldName: currentEditItem.value.sessionName,
        newName: trimmedName,
      })


    } else {
      console.error('修改会话名称失败:', response.msg)
      ElMessage.error(response.msg || '修改会话名称失败')
    }
  } catch (error) {
    console.error('修改会话名称失败:', error)
    ElMessage.error('修改会话名称失败，请稍后重试')
  } finally {
    // 关闭编辑弹窗
    cancelEdit()
    // 编辑成功后关闭主弹窗
    emit('close')
  }
}

// 删除按钮点击事件
const handleDelete = (item: HistoryItem) => {
  console.log('🚀 ~ index.vue:248 ~ handleDelete ~ item:', item)
  currentDeleteItem.value = item
  showDeleteConfirm.value = true
}

// 删除确认弹窗相关方法
const handleDeleteMaskClick = () => {
  cancelDelete()
}

const cancelDelete = () => {
  showDeleteConfirm.value = false
  currentDeleteItem.value = null
}

const confirmDelete = async () => {
  if (!currentDeleteItem.value) {
    return
  }
  console.log('🚀 ~ index.vue:271 ~ confirmDelete ~ currentDeleteItem.value:', currentDeleteItem.value)
  try {
    const response: any = await deleteSession({
      sessionId: currentDeleteItem.value.sessionId,
    })

    if (response.code === 200) {
      // 从列表中移除该项
      const index = historyList.value.findIndex(item => item.sessionId === currentDeleteItem.value?.sessionId)
      if (index > -1) {
        historyList.value.splice(index, 1)
      }

      ElMessage.success('会话删除成功')
      console.log('会话删除成功:', currentDeleteItem.value)

      // 检查被删除的会话是否为当前正在使用的会话
      if (currentDeleteItem.value?.sessionId === chatStore.sessionId) {
        // 如果是当前会话，则执行新建对话的逻辑
        try {
          const groupID = chatStore?.groupId
          const res: any = await createGroupSession({
            groupId: groupID,
          })
          if (res?.code === 200) {
            // 切换到新会话
            chatStore?.setHistoryMessages([])
            chatStore?.setSessionId(res?.data)
            console.log('当前会话已删除，自动创建新会话:', res?.data)
            // 发送sayhello
            const messageConfig = {
              businessID: 'ai_say',
              content: {
                name: 'sayhello',
                data: {},
              },
            }
            await sendCustomMessage({
              data: messageConfig,
            })
          }
        } catch (error) {
          console.error('自动创建新会话失败:', error)
        }
      }
    } else {
      console.error('删除会话失败:', response.msg)
      ElMessage.error(response.msg || '删除会话失败')
    }
  } catch (error) {
    console.error('删除会话失败:', error)
    ElMessage.error('删除会话失败，请稍后重试')
  } finally {
    // 关闭确认弹窗
    cancelDelete()
    // 删除成功后关闭主弹窗
    emit('close')
  }
}


// 滚动处理函数
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target || loadingMore.value || !hasMore.value) return

  const { scrollTop, scrollHeight, clientHeight } = target
  const threshold = 50 // 距离底部50px时触发加载

  if (scrollHeight - scrollTop - clientHeight <= threshold) {
    loadMoreData()
  }
}

// 加载更多数据
const loadMoreData = async () => {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true
  try {
    // 获取当前列表最后一项的sessionId作为main参数
    const lastItem = historyList.value[historyList.value.length - 1]
    const mainParam = lastItem ? lastItem.id : currentMain.value

    const response: any = await getIMSessionList({
      groupId: chatStore.groupId,
      minId: mainParam,
    })

    console.log('🚀 ~ loadMoreData ~ response:', response)

    if (response.code === 200 && response.data) {
      if (response.data.length === 0) {
        // 没有更多数据
        hasMore.value = false
      } else {
        // 追加新数据到列表
        historyList.value.push(...response.data)
        currentMain.value = mainParam
      }
    } else {
      console.error('加载更多数据失败:', response.msg)
      ElMessage.error(response.msg || '加载更多数据失败')
    }
  } catch (error) {
    console.error('加载更多数据失败:', error)
    ElMessage.error('加载更多数据失败，请稍后重试')
  } finally {
    loadingMore.value = false
  }
}

// 获取历史对话列表（首次加载）
const fetchHistoryList = async () => {
  loading.value = true
  // 重置分页状态
  hasMore.value = true
  currentMain.value = 0
  historyList.value = []

  try {
    const response: any = await getIMSessionList({
      groupId: chatStore.groupId,
      minId: 0,
    })
    console.log('🚀 ~ fetchHistoryList ~ response:', response)

    if (response.code === 200 && response.data) {
      // 将IM会话数据转换为历史对话格式
      historyList.value = response.data
      // 如果首次加载的数据为空，设置hasMore为false
      if (response.data.length === 0) {
        hasMore.value = false
      }
    } else {
      console.error('获取IM会话列表失败:', response.msg)
      ElMessage.error(response.msg || '获取会话列表失败')
      historyList.value = []
      hasMore.value = false
    }
  } catch (error) {
    console.error('获取历史对话失败:', error)
    ElMessage.error('获取历史对话失败，请稍后重试')
    historyList.value = []
    hasMore.value = false
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
const handleVisibleChange = () => {
  if (props.visible) {
    fetchHistoryList()
    // 禁止页面滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复页面滚动
    document.body.style.overflow = ''
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  if (props.visible) {
    handleVisibleChange()
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
})

// 监听visible变化
watch(() => props.visible, handleVisibleChange)
</script>

<style lang="scss" scoped>
// 遮罩层动画
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

// 弹窗滑动动画
.slide-up-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

.chat-history-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end; // 改为底部对齐，配合从下往上的动画
  justify-content: center;
  z-index: 1000;

  &__container {
    background: #f2f5fc; // 使用您指定的背景色
    border-radius: 15px 15px 0px 0px; // 使用您指定的圆角
    width: 100%; // 使用您指定的宽度
    height: 815px; // 使用您指定的高度
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    // 确保在小屏幕上不会超出视口
    max-width: 100vw;
    max-height: 100vh;
  }

  &__header {
    position: relative; // 使用相对定位来实现标题居中
    display: flex;
    align-items: center;
    justify-content: center; // 改为居中对齐
    padding: 24px 25px 37px 25px;
    border-bottom: 1px solid #eee;

    .chat-history-modal__title {
      height: 18px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #000000;
      text-align: center;
      // 移除width: 100%，让标题自然居中
    }

    .chat-history-modal__close {
      position: absolute; // 使用绝对定位将关闭按钮固定在右侧
      right: 25px; // 与padding保持一致
      top: 50%; // 垂直居中
      transform: translateY(-50%); // 精确垂直居中
      background: none;
      border: none;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 15px;
      width: 15px;

      img {
        width: 100%;
      }
    }
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止内容溢出，滚动由子容器处理
  }

  &__loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007aff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 12px;
    }
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 12px;
    }
  }

  &__list {
    flex: 1;
    margin: 0 25px;
    display: flex;
    flex-direction: column;
    min-height: 0; // 确保flex子项可以收缩

    .scroller {
      flex: 1;
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      // 优化滚动体验
      -webkit-overflow-scrolling: touch; // iOS平滑滚动
      scrollbar-width: thin; // Firefox细滚动条

      // 自定义滚动条样式（Webkit浏览器）
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;

        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }
      }
    }
  }

  &__loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007aff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }
  }

  &__no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
  }
}

.chat-history-item {
  display: flex;
  align-items: center;
  padding: 13px;
  cursor: pointer;
  background-color: #ffffff;
  margin-bottom: 15px;
  height: 78px; // 固定高度，与虚拟列表的item-size保持一致
  box-sizing: border-box;

  &__avatar {
    width: 50px;
    height: 50px;
    margin-right: 13px;
    flex-shrink: 0;

    .avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  &__info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__title {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #000000;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 10px;
  }

  &__time {
    font-size: 12px;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
    flex-shrink: 0;

    .action-btn {
      width: 19px;
      height: 19px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: background-color 0.2s ease;
      margin: 0 15px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .edit-btn:hover {
      background-color: rgba(64, 158, 255, 0.1);
    }

    .delete-btn:hover {
      background-color: rgba(245, 108, 108, 0.1);
    }
  }

  // 选中状态样式
  &--active {
    background-color: #086DF7 !important;

    .chat-history-item__title {
      color: #ffffff;
    }

    .chat-history-item__time {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 编辑弹窗样式 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; // 确保在历史对话弹窗之上

  .edit-dialog {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 465px;
    height: 232px;
    background: #f2f5fc;
    border-radius: 10px;

    .edit-header {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      position: relative;
      width: 100%;

      .edit-title {
        height: 20px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #000000;
        line-height: 24px;
        text-align: center;
        margin-top: 20px;
      }

      .edit-close {
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
        height: 16px;
        width: 16px;

        img {
          width: 100%;
        }
      }
    }

    .edit-content {
      display: flex;
      width: 425px;
      height: 50px;
      background: #ffffff;
      border-radius: 10px;
      margin: 40px 20px;
      padding-left: 19px;

      .edit-input {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        font-size: 16px;
        font-family: PingFang SC;
        color: #333;
        background: #fff;
        outline: none;
        transition: border-color 0.2s ease;
        line-height: 100%;
        border: none;

        &::placeholder {
          color: #999;
        }

        &:focus {
          outline: none;
        }
      }
    }

    .edit-footer {
      display: flex;
      justify-content: center;
      gap: 12px;

      .btn {
        width: 208px;
        height: 45px;
        border: none;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 400;
        text-align: center;
        line-height: 45px;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 23px;

        &.btn-cancel {
          color: #000000;
          background: #c9cbe3;

          &:hover {
            background: #b8bbdb;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        &.btn-confirm {
          background: #006eff;
          border: 1px solid #006eff;
          color: #fff;

          &:hover:not(:disabled) {
            background: #0056cc;
            border-color: #0056cc;
          }

          &:disabled {
            background: #ccc;
            border-color: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
          }
        }
      }
    }
  }
}

/* 删除确认弹窗样式 */
.delete-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; // 确保在历史对话弹窗之上

  .delete-confirm-dialog {
    width: 350px;
    height: 232px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    background: linear-gradient(to bottom, #c0d3ff 0%, #ffffff 30%);

    .header {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      position: relative;
      width: 100%;
      margin-bottom: 56px;

      .delete-confirm-title {
        height: 20px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #000000;
        line-height: 24px;
        text-align: center;
        margin-top: 20px;
      }

      .close {
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
        height: 16px;
        width: 16px;

        img {
          width: 100%;
        }
      }
    }

    .delete-confirm-content {
      height: 16px;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #313131;
      line-height: 16px;
      margin-bottom: 58px;
    }

    .delete-confirm-footer {
      display: flex;
      justify-content: center;
      gap: 12px;

      .btn {
        width: 150px;
        height: 45px;
        border: none;
        font-size: 14px;
        text-align: center;
        line-height: 45px;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 23px;

        &.btn-cancel {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #000000;
          background: #c9cbe3;

          &:hover {
            background: #b8bbdb;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        &.btn-confirm {
          background: #006eff;
          border: 1px solid #006eff;
          color: #fff;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;

          &:hover:not(:disabled) {
            background: #0056cc;
            border-color: #0056cc;
          }

          &:disabled {
            background: #ccc;
            border-color: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
          }
        }
      }
    }
  }
}
</style>
