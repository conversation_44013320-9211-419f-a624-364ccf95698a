import TUIChatEngine, { IMessageModel } from '@tencentcloud/chat-uikit-engine'

/**
 * 新格式的IM回调数据接口
 */
interface NewIMCallbackData {
  id: number
  ctime: number
  msgKey: string
  callbackCommand: string
  fromAccount: string
  toAccount: string
  groupId: string
  content: string // JSON字符串，包含MsgBody数组
  eventTime: number
  sessionId: string
  msgSeq: number
  random: number
  msgId: string
  userAvatar: string
  userNickname: string
}

/**
 * 原始IM回调数据接口（向后兼容）
 */
interface OriginalIMCallbackData {
  MsgId: string
  MsgBody: Array<{
    MsgType: string
    MsgContent: {
      Data: string
      Desc?: string
      Ext?: string
      Sound?: string
    }
  }>
  From_Account: string
  GroupId?: string
  MsgTime: number
  MsgSeq?: number
  Random?: number
  MsgPriority?: string
  CloudCustomData?: string
  CallbackCommand?: string
  EventTime?: number
  OnlineOnlyFlag?: number
  Operator_Account?: string
  Type?: string
}

/**
 * IM回调数据处理器类
 */
// 不返回data.businessID !== 'hidden_message' && data.businessID !== 'ai_signal' && data.businessID !== 'ai_say' && data.businessID !== 'ai_event' && data.businessID !== 'say_hidden_message'的项目
const filterBusinessID = ['hidden_message', 'ai_signal', 'ai_say', 'ai_event', 'say_hidden_message']

export class IMCallbackHandler {
  private static readonly TYPES = TUIChatEngine.TYPES

  /**
   * 处理IM回调数据并转换为TUIKit消息格式
   * @param callbackDataList 可以是新格式数组、原始格式数组或单个对象
   * @param reverse 是否反转返回的消息数组顺序，默认为false（保持原顺序）
   * @returns 转换后的TUIKit消息对象数组
   */
  static processIMCallbacks(callbackDataList: NewIMCallbackData[] | OriginalIMCallbackData[] | NewIMCallbackData | OriginalIMCallbackData, reverse: boolean = false): IMessageModel[] {
    try {
      // 统一转换为数组格式
      const dataArray = Array.isArray(callbackDataList) ? callbackDataList : [callbackDataList]

      console.log(`开始处理IM回调数据，共 ${dataArray.length} 条:`, dataArray)

      const convertedMessages: IMessageModel[] = []

      // 遍历处理每个回调数据
      for (let i = 0; i < dataArray.length; i++) {
        const callbackData = dataArray[i]
        console.log(`处理第 ${i + 1}/${dataArray.length} 条回调数据:`, callbackData)

        let convertedMessage: IMessageModel | null = null

        // 根据数据格式选择处理方式
        if (this.isNewFormat(callbackData)) {
          convertedMessage = this.convertNewFormatToTUIKit(callbackData as NewIMCallbackData)
        } else {
          convertedMessage = this.convertOriginalFormatToTUIKit(callbackData as OriginalIMCallbackData)
        }

        if (convertedMessage) {
          // 获取businessID（兼容新旧格式）
          let businessID = ''
          try {
            if (convertedMessage.getMessageContent && typeof convertedMessage.getMessageContent === 'function') {
              const content = convertedMessage.getMessageContent()
              businessID = content.businessID || ''
            }
          } catch (e) {
            console.warn('获取businessID失败:', e)
          }
          if (!filterBusinessID.includes(businessID)) {
            convertedMessages.push(convertedMessage)
            console.log(`第 ${i + 1} 条消息转换成功:`, convertedMessage.ID)
          } else {
            console.log(`第 ${i + 1} 条消息被过滤，businessID:`, businessID)
          }
        } else {
          console.warn(`第 ${i + 1} 条消息转换失败`)
        }
      }

      console.log(`IM回调数据处理完成，成功转换 ${convertedMessages.length} 条消息`)

      // 根据reverse参数决定是否反转消息数组顺序
      const finalMessages = reverse ? convertedMessages.reverse() : convertedMessages
      console.log(`消息数组${reverse ? '已反转' : '保持原'}顺序`)

      return finalMessages
    } catch (error) {
      console.error('处理IM回调数据失败:', error)
      return []
    }
  }

  /**
   * 判断是否为新格式的回调数据
   * @param data 回调数据
   * @returns 是否为新格式
   */
  private static isNewFormat(data: any): boolean {
    return (
      typeof data === 'object' &&
      data !== null &&
      typeof data.id === 'number' &&
      typeof data.ctime === 'number' &&
      typeof data.fromAccount === 'string' &&
      typeof data.content === 'string' &&
      typeof data.msgId === 'string'
    )
  }

  /**
   * 转换新格式回调数据为TUIKit消息格式
   * @param data 新格式回调数据
   * @returns TUIKit消息对象
   */
  private static convertNewFormatToTUIKit(data: NewIMCallbackData): IMessageModel | null {
    try {
      // 解析content字符串获取MsgBody
      let msgBodyArray: any[]
      try {
        msgBodyArray = JSON.parse(data.content)
      } catch (e) {
        console.error('解析新格式content失败:', e, data.content)
        return null
      }

      if (!Array.isArray(msgBodyArray) || msgBodyArray.length === 0) {
        console.warn('新格式content解析后不是有效的数组:', msgBodyArray)
        return null
      }

      const msgBody = msgBodyArray[0]
      if (!msgBody || msgBody.MsgType !== 'TIMCustomElem') {
        console.warn('不支持的消息类型:', msgBody?.MsgType)
        return null
      }

      // 解析自定义消息内容
      let customData: any = {}
      try {
        if (msgBody.MsgContent?.Data) {
          customData = JSON.parse(msgBody.MsgContent.Data)
        }
      } catch (e) {
        console.error('解析自定义消息数据失败:', e)
        return null
      }

      // 应用消息过滤逻辑
      if (customData.businessID === 'ai_message' && Array.isArray(customData?.tips) && customData.tips.length > 0 && !customData?.text?.length) {
        console.log('过滤掉 tips 消息:', {
          businessID: customData.businessID,
          tipsLength: customData.tips?.length,
          hasText: !!customData?.text?.length,
          msgId: data.msgId,
        })
        return null
      }

      // 构建基础时间戳和会话信息
      const timestamp = data.eventTime || data.ctime
      const conversationID = data.groupId ? `GROUP${data.groupId}` : `C2C${data.fromAccount}`
      const conversationType = data.groupId ? this.TYPES.CONV_GROUP : this.TYPES.CONV_C2C

      // 构建消息对象
      const tuikitMessage = this.buildTUIKitMessage({
        msgId: data.msgId,
        fromAccount: data.fromAccount,
        groupId: data.groupId,
        msgTime: Math.floor(timestamp / 1000), // 转换为秒
        msgSeq: data.msgSeq,
        random: data.random,
        msgBody: msgBody,
        customData: customData,
        conversationID: conversationID,
        conversationType: conversationType,
        originalId: data.id, // 新增：传递原始数据的id字段
        userAvatar: data.userAvatar, // 新增：传递用户头像字段
        userNickname: data.userNickname, // 新增：传递用户昵称字段
      })

      return tuikitMessage
    } catch (error) {
      console.error('转换新格式回调数据失败:', error)
      return null
    }
  }

  /**
   * 转换原始格式回调数据为TUIKit消息格式（向后兼容）
   * @param data 原始格式回调数据
   * @returns TUIKit消息对象
   */
  private static convertOriginalFormatToTUIKit(data: OriginalIMCallbackData): IMessageModel | null {
    try {
      // 验证必要字段
      if (!data || !data.MsgId || !data.MsgBody || !Array.isArray(data.MsgBody)) {
        console.warn('原始格式IM回调数据格式不正确:', data)
        return null
      }

      // 获取消息体
      const msgBody = data.MsgBody[0]
      if (!msgBody || msgBody.MsgType !== 'TIMCustomElem') {
        console.warn('不支持的消息类型:', msgBody?.MsgType)
        return null
      }

      // 解析自定义消息内容
      let customData: any = {}
      try {
        if (msgBody.MsgContent?.Data) {
          customData = JSON.parse(msgBody.MsgContent.Data)
        }
      } catch (e) {
        console.error('解析自定义消息数据失败:', e)
        return null
      }

      // 应用消息过滤逻辑
      if (customData.businessID === 'ai_message' && Array.isArray(customData?.tips) && customData.tips.length > 0 && !customData?.text?.length) {
        console.log('过滤掉 tips 消息:', {
          businessID: customData.businessID,
          tipsLength: customData.tips?.length,
          hasText: !!customData?.text?.length,
          msgId: data.MsgId,
        })
        return null
      }

      // 构建基础会话信息
      const conversationID = data.GroupId ? `GROUP${data.GroupId}` : `C2C${data.From_Account}`
      const conversationType = data.GroupId ? this.TYPES.CONV_GROUP : this.TYPES.CONV_C2C

      // 构建消息对象
      const tuikitMessage = this.buildTUIKitMessage({
        msgId: data.MsgId,
        fromAccount: data.From_Account,
        groupId: data.GroupId,
        msgTime: data.MsgTime,
        msgSeq: data.MsgSeq,
        random: data.Random,
        msgBody: msgBody,
        customData: customData,
        conversationID: conversationID,
        conversationType: conversationType,
        cloudCustomData: data.CloudCustomData,
        originalId: data.MsgId, // 新增：对于原始格式，使用MsgId作为originalId
        // 注意：原始格式数据中可能没有userAvatar和userNickname字段，使用默认值
        userAvatar: '', // 原始格式暂时使用空字符串
        userNickname: undefined, // 原始格式使用fromAccount作为昵称
      })

      return tuikitMessage
    } catch (error) {
      console.error('转换原始格式回调数据失败:', error)
      return null
    }
  }

  /**
   * 构建TUIKit消息对象的通用方法
   * @param params 构建参数
   * @returns TUIKit消息对象
   */
  private static buildTUIKitMessage(params: {
    msgId: string
    fromAccount: string
    groupId?: string
    msgTime: number
    msgSeq?: number
    random?: number
    msgBody: any
    customData: any
    conversationID: string
    conversationType: number
    cloudCustomData?: string
    originalId?: number | string // 新增：原始数据的id字段
    userAvatar?: string // 新增：用户头像字段
    userNickname?: string // 新增：用户昵称字段
  }): IMessageModel {
    const {
      msgId,
      fromAccount,
      groupId,
      msgTime,
      msgSeq = 0,
      random = 0,
      msgBody,
      customData,
      conversationID,
      conversationType,
      cloudCustomData = '',
      originalId,
      userAvatar = '',
      userNickname,
    } = params

    // 构建 _elements 数组结构
    const elements = [
      {
        type: msgBody.MsgType,
        content: {
          data: msgBody.MsgContent.Data,
          description: msgBody.MsgContent.Desc || '',
          extension: msgBody.MsgContent.Ext || '',
        },
      },
    ]

    // 构建嵌套的 _message 对象
    const _message = {
      ID: msgId,
      conversationID: conversationID,
      conversationType: conversationType,
      time: msgTime,
      sequence: msgSeq,
      clientSequence: Date.now(),
      random: random,
      priority: 'Normal',
      nick: userNickname || fromAccount, // 优先使用userNickname，回退到fromAccount
      avatar: userAvatar, // 使用userAvatar字段
      isPeerRead: false,
      nameCard: '',
      hasRiskContent: false,
      _elements: elements,
      isPlaceMessage: 0,
      isRevoked: false,
      from: fromAccount,
      to: groupId || fromAccount,
      flow: fromAccount?.includes('agent') ? 'in' : 'out',
      isSystemMessage: false,
      protocol: 'JSON',
      isResend: false,
      isRead: true,
      status: 'success',
      _onlineOnlyFlag: false,
      _groupAtInfoList: [],
      _relayFlag: false,
      atUserList: [],
      cloudCustomData: cloudCustomData,
      isDeleted: false,
      isModified: false,
      _isExcludedFromUnreadCount: false,
      _isExcludedFromLastMessage: false,
      clientTime: msgTime,
      senderTinyID: msgId,
      readReceiptInfo: {
        timestamp: 0,
      },
      needReadReceipt: false,
      version: 0,
      isBroadcastMessage: false,
      isSupportExtension: false,
      revoker: '',
      revokerInfo: {
        userID: '',
        nick: '',
        avatar: '',
      },
      revokeReason: '',
      payload: {
        data: msgBody.MsgContent.Data,
        description: msgBody.MsgContent.Desc || '',
        extension: msgBody.MsgContent.Ext || '',
      },
      type: msgBody.MsgType,
      originalId: originalId, // 新增：原始数据的id字段
    }

    // 构造完整的TUIKit消息对象，包含嵌套结构和扁平化属性
    const tuikitMessage: any = {
      messageHandlers: {},
      _message: _message,
      progress: 0,
      reactionList: [],
      // 扁平化的外层属性（与_message重复但必需）
      ID: msgId,
      conversationID: conversationID,
      conversationType: conversationType,
      time: msgTime, // 修复：使用秒级时间戳，与内层_message.time保持一致
      sequence: msgSeq, // 修复：使用实际sequence值，与内层_message.sequence保持一致
      clientSequence: Date.now(),
      random: random,
      priority: 'Normal',
      nick: userNickname || fromAccount, // 优先使用userNickname，回退到fromAccount
      avatar: userAvatar, // 使用userAvatar字段
      isPeerRead: false,
      nameCard: '',
      hasRiskContent: false,
      isPlaceMessage: 0,
      isRevoked: false,
      from: fromAccount,
      to: groupId || fromAccount,
      flow: fromAccount?.includes('agent') ? 'in' : 'out',
      isSystemMessage: false,
      protocol: 'JSON',
      isResend: false,
      isRead: true,
      status: 'success',
      atUserList: [],
      cloudCustomData: cloudCustomData,
      isDeleted: false,
      isModified: false,
      clientTime: msgTime,
      senderTinyID: msgId.split('-')[0], // 只取ID的第一部分
      readReceiptInfo: {
        timestamp: 0,
      },
      needReadReceipt: false,
      version: 0,
      isBroadcastMessage: false,
      isSupportExtension: false,
      revoker: '',
      revokerInfo: {
        userID: '',
        nick: '',
        avatar: '',
      },
      revokeReason: '',
      payload: {
        data: msgBody.MsgContent.Data,
        description: msgBody.MsgContent.Desc || '',
        extension: msgBody.MsgContent.Ext || '',
      },
      type: msgBody.MsgType,
      originalId: originalId, // 新增：原始数据的id字段
      // 添加必要的方法
      getMessageContent: function () {
        return {
          custom: this.payload.data,
          businessID: customData.businessID || '',
          showName: this.nick || this.from || '', // 优先使用nick（用户昵称），回退到from
        }
      },
      updateProperties: function () {
        // 空实现，避免调用错误
      },
      resendMessage: function () {
        // 空实现
      },
      revokeMessage: function () {
        // 空实现
      },
    }
    //
    // 返回消息对象（类型断言为 IMessageModel）
    return tuikitMessage as IMessageModel
  }

  /**
   * 静态方法：用于测试新格式数据转换
   * @param testData 测试数据
   * @param reverse 是否反转结果数组
   * @returns 转换结果
   */
  static testNewFormat(testData: NewIMCallbackData[], reverse: boolean = false): IMessageModel[] {
    console.log('测试新格式数据转换:', testData)
    return this.processIMCallbacks(testData, reverse)
  }

  /**
   * 静态方法：测试数组反转功能
   * @param testData 测试数据
   * @returns 包含原顺序和反转顺序的测试结果
   */
  static testReverseFunction(testData: NewIMCallbackData[] | OriginalIMCallbackData[]): {
    original: IMessageModel[]
    reversed: IMessageModel[]
    isReversed: boolean
  } {
    console.log('测试数组反转功能:', testData)

    const originalResult = this.processIMCallbacks(testData, false)
    const reversedResult = this.processIMCallbacks(testData, true)

    // 验证反转是否正确（比较第一个和最后一个元素的ID）
    const isReversed = originalResult.length > 1 && originalResult[0]?.ID === reversedResult[reversedResult.length - 1]?.ID && originalResult[originalResult.length - 1]?.ID === reversedResult[0]?.ID

    console.log('反转测试结果:', {
      originalCount: originalResult.length,
      reversedCount: reversedResult.length,
      isCorrectlyReversed: isReversed,
    })

    return {
      original: originalResult,
      reversed: reversedResult,
      isReversed: isReversed,
    }
  }
}
